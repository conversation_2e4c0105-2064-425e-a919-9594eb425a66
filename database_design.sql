-- Qwen Solve API 数据库设计
-- 数据库: t_solve_go_api
-- 表前缀: qwen_solve_
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci

-- 1. 题库表 - 存储所有题目信息
CREATE TABLE `qwen_solve_questions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hash_key` varchar(64) NOT NULL COMMENT '缓存键名(哈希值)',
  `quest_type` enum('单选题','多选题','判断题') NOT NULL COMMENT '题目类型',
  `quest_content` text NOT NULL COMMENT '题目内容',
  `quest_options` json NOT NULL COMMENT '题目选项(JSON格式)',
  `answer` json NOT NULL COMMENT '正确答案(JSON格式)',
  `analysis` text NOT NULL COMMENT '答案解析',
  `user_image` varchar(500) DEFAULT NULL COMMENT '用户提交的图片URL',
  `image_url` varchar(500) DEFAULT NULL COMMENT '标准图片URL(管理员维护)',
  `hash_raw` text DEFAULT NULL COMMENT '缓存键哈希前的原文',
  `qwen_raw` longtext DEFAULT NULL COMMENT 'qwen-vl-plus返回的原始数据',
  `qwen_parsed` text DEFAULT NULL COMMENT '请求qwen-plus前拼装的JSON字符串',
  `qwen_plus_raw` longtext DEFAULT NULL COMMENT 'qwen-plus返回的原始数据',
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已验证(0:未验证,1:已验证)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_hash_key` (`hash_key`),
  KEY `idx_quest_type` (`quest_type`),
  KEY `idx_is_verified` (`is_verified`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题库表';

-- 2. 请求日志表 - 记录所有API请求
CREATE TABLE `qwen_solve_request_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(64) NOT NULL COMMENT '请求唯一标识',
  `user_image_url` varchar(500) NOT NULL COMMENT '用户提交的图片URL',
  `response_data` longtext DEFAULT NULL COMMENT '返回给用户的数据(JSON格式)',
  `is_redis_hit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否Redis缓存命中(0:未命中,1:命中)',
  `is_mysql_hit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否MySQL缓存命中(0:未命中,1:命中)',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `processing_time` int(11) DEFAULT NULL COMMENT '处理时间(毫秒)',
  `request_ip` varchar(45) DEFAULT NULL COMMENT '请求IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_id` (`request_id`),
  KEY `idx_user_image_url` (`user_image_url`),
  KEY `idx_is_redis_hit` (`is_redis_hit`),
  KEY `idx_is_mysql_hit` (`is_mysql_hit`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='请求日志表';

-- 3. 系统配置表 - 存储系统配置信息(预留扩展)
CREATE TABLE `qwen_solve_system_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键名',
  `config_value` text DEFAULT NULL COMMENT '配置值',
  `config_type` enum('string','int','float','bool','json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用(0:禁用,1:启用)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 4. API统计表 - 记录API调用统计(预留扩展)
CREATE TABLE `qwen_solve_api_statistics` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `date` date NOT NULL COMMENT '统计日期',
  `total_requests` int(11) NOT NULL DEFAULT 0 COMMENT '总请求数',
  `success_requests` int(11) NOT NULL DEFAULT 0 COMMENT '成功请求数',
  `failed_requests` int(11) NOT NULL DEFAULT 0 COMMENT '失败请求数',
  `redis_hit_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Redis命中次数',
  `mysql_hit_count` int(11) NOT NULL DEFAULT 0 COMMENT 'MySQL命中次数',
  `qwen_api_calls` int(11) NOT NULL DEFAULT 0 COMMENT 'Qwen API调用次数',
  `avg_processing_time` decimal(10,2) DEFAULT NULL COMMENT '平均处理时间(毫秒)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date` (`date`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API统计表';

-- 插入默认系统配置
INSERT INTO `qwen_solve_system_configs` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('redis_cache_ttl', '86400', 'int', 'Redis缓存过期时间(秒)'),
('max_image_size', '10485760', 'int', '最大图片大小(字节)'),
('qwen_api_timeout', '30', 'int', 'Qwen API超时时间(秒)'),
('enable_request_log', 'true', 'bool', '是否启用请求日志'),
('max_log_retention_days', '30', 'int', '日志保留天数');
