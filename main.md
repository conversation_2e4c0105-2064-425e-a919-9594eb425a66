- 使用golang开发一个api服务。需要使用gin框架。mysql8以及redis。

- 开发过程中使用开发模式进行开发，但需要使用远程的正式的mysql与redis，具体后续用到的配置参数查看config.md文件


## 业务要求；

1. 高标准、高规范化开发；
2. 尽可能的将业务模块封装成独立的方法，以便于后续业务功能扩展；
3. 规范化的目录结构；
4. 规范命名；



## 业务逻辑介绍


- 核心功能，用户携带一个图片url请求API服务，我们API在完成业务处理后给用户返回数据。以下是业务逻辑：
- 这里需要一个ProcessContext方法存储部分内容然后贯穿整个流程，后面会复用

1. 对用户提交的图片进行简单的验证，只验证图片是否可访问即可。若图片不可访问，则返回错误：图片不存在,上传失败,请联系管理员处理！
    - 用户图片存储ProcessContext；字段为user_imgurl

2. 将图片url提交给qwen-vl-plus模型，得到qwen的返回数据。（调用qwen-vl-plus模型的方法名定义：CallQwenVlPlus。方法查阅qwen_vl_plus_type.md文件）
    - qwen-vl-plus 的返回的原始数据存储ProcessContext；字段为qwen_raw_data（必须是完整的原始数据，不可以进行任何处理）

3. 将qwen-vl-plus模型返回的原始数据进行处理解析（方法名定义：FormatQwenData。具体业务逻辑查看FormatQwenData.md文件）

4. 复用ProcessContext中的数据制作缓存键名称

    4.1 将ProcessContext中的quest_type、quest_content、quest_option取出纯文本拼接到一起
    - 示例； quest_type + quest_content + quest_option_A + quest_option_B + quest_option_C + quest_option_D
    - 注意判断题只有两个选项，所以只需要拼接两个选项即可
    
    4.2 将拼接后的字符串使用正则清洗空格换行符以及标点符号(`[\s\p{P}\x{3000}]+`)

    4.3 将清洗后的字符串进行哈希处理，然后生成缓存键的键名。这里需要注意相同内容的字符串生成的哈希值必须相同，才能保证缓存的命中率。不同内容的字符串生成的哈希值必须不同，才能保证缓存的隔离性。

5. redis查询缓存键是否存在
    4.1 redis存在     →   则直返回对应的valve给用户。
    4.2 redis不存在   →   降级mysql查询。    
                4.2.1 mysql存在       →， 回写redis（方法名定义：WriteToRedis。具体业务逻辑查看WriteToRedis.md文件），然后给用户返回对应的valve。
                4.2.2 mysql不存在     →， 降级调用qwen-plus模型，调用qwen-plus模型的方法名定义：CallQwenPlus。具体业务逻辑查看qwen_plus_type.md文件）

5. 将qwen-plus模型的返回数据进行拆分并于之前ProcessContext保存的数据进行入库（方法名定义：SaveToDatabase。具体业务逻辑查看SaveToDatabase.md文件）

6. 入库后在进行redis的写入。（方法名定义：SaveDeepseekToDatabase。具体业务逻辑查看SaveToDatabase.md文件）

7. 最终返回给用户的数据是一个可能包含多个问题的数组。



## 管理功能

1. 请求日志预览接口
    - 记录用户每次请求的图片url
    - 记录系统返回给用户的数据
    - 记录请求时间
    - 记录当前请求是否被redis命中
    - 如果本次请求存在错误，则记录错误信息。
    - 允许删除记录（软删除）
    - 这个api接口无需鉴权即可使用，无需考虑风险问题。仅完成功能要求即可。

    仅实现API接口功能，前端界面将有前端技术团队完成。


2. 题库管理功能
    - 一个允许对题库进行增删改查的api接口
    - 需要支持除自增id外所有字段的修改的api