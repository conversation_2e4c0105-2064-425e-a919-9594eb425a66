package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"qwen-solve/internal/config"
	"qwen-solve/internal/handler"
	"qwen-solve/internal/service"
	"qwen-solve/pkg/database"
	"qwen-solve/pkg/redis"
	"qwen-solve/pkg/utils"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库连接
	if err := database.InitMySQL(&cfg.Database); err != nil {
		log.Fatalf("Failed to initialize MySQL: %v", err)
	}
	defer database.CloseMySQL()

	// 初始化Redis连接
	if err := redis.InitRedis(&cfg.Redis); err != nil {
		log.Fatalf("Failed to initialize Redis: %v", err)
	}
	defer redis.CloseRedis()

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建Gin引擎
	router := gin.New()

	// 添加中间件
	router.Use(utils.RequestLogger())
	router.Use(utils.Recovery())
	router.Use(utils.CORS())

	// 创建服务实例
	solveService := service.NewSolveService(cfg)
	logService := service.NewLogService()
	databaseService := service.NewDatabaseService()

	// 创建处理器实例
	solveHandler := handler.NewSolveHandler(solveService)
	logHandler := handler.NewLogHandler(logService)
	questionHandler := handler.NewQuestionHandler(databaseService)

	// 设置路由
	setupRoutes(router, solveHandler, logHandler, questionHandler)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}

	// 启动服务器
	go func() {
		fmt.Printf("Server starting on %s:%d\n", cfg.Server.Host, cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("Server shutting down...")

	// 优雅关闭服务器
	if err := server.Close(); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	fmt.Println("Server exited")
}

// setupRoutes 设置路由
func setupRoutes(router *gin.Engine, solveHandler *handler.SolveHandler, logHandler *handler.LogHandler, questionHandler *handler.QuestionHandler) {
	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		utils.SuccessResponse(c, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
			"version":   "1.0.0",
		})
	})

	// API路由组
	api := router.Group("/api/v1")
	{
		// 解题相关路由
		solve := api.Group("/solve")
		{
			solve.POST("", solveHandler.SolveImage)                    // 图片解题
			solve.POST("/validate", solveHandler.ValidateImage)        // 验证图片
			solve.POST("/reprocess", solveHandler.ReprocessImage)      // 重新处理图片
			solve.GET("/history", solveHandler.GetSolveHistory)        // 获取解题历史
			solve.GET("/statistics", solveHandler.GetSolveStatistics)  // 获取解题统计
			solve.DELETE("/cache/:hash_key", solveHandler.ClearCache)  // 清除缓存
			solve.POST("/cache/:hash_key/refresh", solveHandler.RefreshCache) // 刷新缓存
		}

		// 日志管理路由
		logs := api.Group("/logs")
		{
			logs.GET("", logHandler.GetRequestLogs)           // 获取请求日志列表
			logs.GET("/:id", logHandler.GetRequestLogByID)    // 根据ID获取请求日志
			logs.DELETE("/:id", logHandler.DeleteRequestLog)  // 删除请求日志
			logs.GET("/statistics", logHandler.GetLogStatistics) // 获取日志统计
			logs.GET("/errors", logHandler.GetErrorLogs)      // 获取错误日志
			logs.GET("/recent", logHandler.GetRecentLogs)     // 获取最近日志
		}

		// 题库管理路由
		questions := api.Group("/questions")
		{
			questions.GET("", questionHandler.GetQuestions)           // 获取题目列表
			questions.GET("/:id", questionHandler.GetQuestionByID)    // 根据ID获取题目
			questions.POST("", questionHandler.CreateQuestion)        // 创建题目
			questions.PUT("/:id", questionHandler.UpdateQuestion)     // 更新题目
			questions.DELETE("/:id", questionHandler.DeleteQuestion)  // 删除题目
			questions.GET("/search", questionHandler.SearchQuestions) // 搜索题目
		}
	}

	// 404处理
	router.NoRoute(func(c *gin.Context) {
		utils.ErrorResponse(c, 404, "接口不存在")
	})

	// 405处理
	router.NoMethod(func(c *gin.Context) {
		utils.ErrorResponse(c, 405, "请求方法不允许")
	})
}
