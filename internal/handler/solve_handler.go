package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"qwen-solve/internal/model"
	"qwen-solve/internal/service"
	"qwen-solve/pkg/utils"
)

// SolveHandler 解题处理器
type SolveHandler struct {
	solveService service.SolveService
}

// NewSolveHandler 创建解题处理器实例
func NewSolveHandler(solveService service.SolveService) *SolveHandler {
	return &SolveHandler{
		solveService: solveService,
	}
}

// SolveImage 图片解题接口
// @Summary 图片解题
// @Description 上传图片URL进行题目识别和解答
// @Tags solve
// @Accept json
// @Produce json
// @Param request body model.SolveImageRequest true "解题请求"
// @Success 200 {object} model.APIResponse{data=model.SolveImageResponse} "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/solve [post]
func (h *SolveHandler) SolveImage(c *gin.Context) {
	var req model.SolveImageRequest
	
	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, err)
		return
	}

	// 获取客户端信息
	requestIP := utils.GetClientIP(c)
	userAgent := utils.GetUserAgent(c)

	// 调用解题服务
	response, err := h.solveService.SolveImage(req.ImageURL, requestIP, userAgent)
	if err != nil {
		// 根据错误类型返回不同的错误码
		if err.Error() == model.ErrorMessages[model.CodeImageNotFound] {
			utils.ErrorResponse(c, model.CodeImageNotFound, err.Error())
		} else if err.Error() == model.ErrorMessages[model.CodeQuestionTypeError] {
			utils.ErrorResponse(c, model.CodeQuestionTypeError, err.Error())
		} else if err.Error() == model.ErrorMessages[model.CodeQuestionInvalid] {
			utils.ErrorResponse(c, model.CodeQuestionInvalid, err.Error())
		} else {
			utils.InternalErrorResponse(c, err)
		}
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, response)
}

// ValidateImage 验证图片接口
// @Summary 验证图片
// @Description 验证图片URL是否可访问
// @Tags solve
// @Accept json
// @Produce json
// @Param request body model.SolveImageRequest true "图片验证请求"
// @Success 200 {object} model.APIResponse "验证成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/solve/validate [post]
func (h *SolveHandler) ValidateImage(c *gin.Context) {
	var req model.SolveImageRequest
	
	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, err)
		return
	}

	// 验证图片
	if err := h.solveService.ValidateImageOnly(req.ImageURL); err != nil {
		utils.ErrorResponse(c, model.CodeImageNotFound, err.Error())
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, gin.H{"message": "图片验证成功"})
}

// ReprocessImage 重新处理图片接口
// @Summary 重新处理图片
// @Description 强制重新处理图片，不使用缓存
// @Tags solve
// @Accept json
// @Produce json
// @Param request body model.SolveImageRequest true "重新处理请求"
// @Success 200 {object} model.APIResponse{data=model.SolveImageResponse} "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/solve/reprocess [post]
func (h *SolveHandler) ReprocessImage(c *gin.Context) {
	var req model.SolveImageRequest
	
	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, err)
		return
	}

	// 获取客户端信息
	requestIP := utils.GetClientIP(c)
	userAgent := utils.GetUserAgent(c)

	// 调用重新处理服务
	response, err := h.solveService.ReprocessImage(req.ImageURL, requestIP, userAgent)
	if err != nil {
		// 根据错误类型返回不同的错误码
		if err.Error() == model.ErrorMessages[model.CodeImageNotFound] {
			utils.ErrorResponse(c, model.CodeImageNotFound, err.Error())
		} else if err.Error() == model.ErrorMessages[model.CodeQuestionTypeError] {
			utils.ErrorResponse(c, model.CodeQuestionTypeError, err.Error())
		} else if err.Error() == model.ErrorMessages[model.CodeQuestionInvalid] {
			utils.ErrorResponse(c, model.CodeQuestionInvalid, err.Error())
		} else {
			utils.InternalErrorResponse(c, err)
		}
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, response)
}

// GetSolveHistory 获取解题历史接口
// @Summary 获取解题历史
// @Description 获取解题历史记录
// @Tags solve
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} model.APIResponse{data=model.RequestLogListResponse} "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/solve/history [get]
func (h *SolveHandler) GetSolveHistory(c *gin.Context) {
	// 获取分页参数
	page := 1
	pageSize := 20
	
	if p, exists := c.GetQuery("page"); exists {
		if pageInt, err := strconv.Atoi(p); err == nil && pageInt > 0 {
			page = pageInt
		}
	}
	
	if ps, exists := c.GetQuery("page_size"); exists {
		if pageSizeInt, err := strconv.Atoi(ps); err == nil && pageSizeInt > 0 && pageSizeInt <= 100 {
			pageSize = pageSizeInt
		}
	}

	// 获取解题历史
	logs, total, err := h.solveService.GetSolveHistory(page, pageSize)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 转换为响应格式
	var responses []*model.RequestLogResponse
	for _, log := range logs {
		responses = append(responses, log.ToResponse())
	}

	// 计算总页数
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	// 返回分页响应
	utils.SuccessResponse(c, map[string]interface{}{
		"list":        responses,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	})
}

// GetSolveStatistics 获取解题统计接口
// @Summary 获取解题统计
// @Description 获取解题统计信息
// @Tags solve
// @Accept json
// @Produce json
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Success 200 {object} model.APIResponse{data=model.RequestLogStatistics} "成功"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/solve/statistics [get]
func (h *SolveHandler) GetSolveStatistics(c *gin.Context) {
	// 获取日期参数
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// 获取统计信息
	stats, err := h.solveService.GetSolveStatistics(startDate, endDate)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, stats)
}

// ClearCache 清除缓存接口
// @Summary 清除缓存
// @Description 清除指定题目的缓存
// @Tags solve
// @Accept json
// @Produce json
// @Param hash_key path string true "缓存键"
// @Success 200 {object} model.APIResponse "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/solve/cache/{hash_key} [delete]
func (h *SolveHandler) ClearCache(c *gin.Context) {
	hashKey := c.Param("hash_key")
	
	if hashKey == "" {
		utils.ErrorResponse(c, model.CodeBadRequest, "缓存键不能为空")
		return
	}

	// 清除缓存
	if err := h.solveService.ClearCache(hashKey); err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, gin.H{"message": "缓存清除成功"})
}

// RefreshCache 刷新缓存接口
// @Summary 刷新缓存
// @Description 从数据库重新加载数据到缓存
// @Tags solve
// @Accept json
// @Produce json
// @Param hash_key path string true "缓存键"
// @Success 200 {object} model.APIResponse "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/solve/cache/{hash_key}/refresh [post]
func (h *SolveHandler) RefreshCache(c *gin.Context) {
	hashKey := c.Param("hash_key")
	
	if hashKey == "" {
		utils.ErrorResponse(c, model.CodeBadRequest, "缓存键不能为空")
		return
	}

	// 刷新缓存
	if err := h.solveService.RefreshCache(hashKey); err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, gin.H{"message": "缓存刷新成功"})
}
