package service

import (
	"fmt"

	"qwen-solve/internal/context"
	"qwen-solve/internal/model"
	"qwen-solve/internal/repository"
	"qwen-solve/pkg/utils"
)

// DatabaseService 数据库服务接口
type DatabaseService interface {
	SaveToDatabase(ctx *context.ProcessContext) error
	GetFromDatabase(ctx *context.ProcessContext) ([]*model.QuestionResponse, error)
	GetQuestionsByHashKey(hashKey string) ([]*model.QuestionResponse, error)
	UpdateQuestion(id uint64, req *model.UpdateQuestionRequest) error
	DeleteQuestion(id uint64) error
	GetQuestionByID(id uint64) (*model.Question, error)
	ListQuestions(req *model.QuestionListRequest) ([]*model.Question, int64, error)
	CreateQuestion(req *model.CreateQuestionRequest) (*model.Question, error)
	SearchQuestions(keyword string, questType *model.QuestionType, isVerified *bool, page, pageSize int) ([]*model.Question, int64, error)
}

// databaseService 数据库服务实现
type databaseService struct {
	questionRepo repository.QuestionRepository
}

// NewDatabaseService 创建数据库服务实例
func NewDatabaseService() DatabaseService {
	return &databaseService{
		questionRepo: repository.NewQuestionRepository(),
	}
}

// SaveToDatabase 保存数据到数据库
func (s *databaseService) SaveToDatabase(ctx *context.ProcessContext) error {
	// 将ProcessContext转换为Question模型
	question := ctx.ToQuestion()
	
	// 保存到数据库
	if err := s.questionRepo.Create(question); err != nil {
		return fmt.Errorf("failed to save question to database: %w", err)
	}
	
	return nil
}

// GetFromDatabase 从数据库获取数据
func (s *databaseService) GetFromDatabase(ctx *context.ProcessContext) ([]*model.QuestionResponse, error) {
	// 根据哈希键查询数据库
	questions, err := s.questionRepo.GetByHashKey(ctx.HashKey)
	if err != nil {
		return nil, fmt.Errorf("failed to get questions from database: %w", err)
	}
	
	if len(questions) == 0 {
		// 数据库中没有找到数据
		return nil, nil
	}
	
	// 设置MySQL命中标志
	ctx.MysqlHit = true
	
	// 转换为响应格式
	var responses []*model.QuestionResponse
	for _, question := range questions {
		responses = append(responses, question.ToResponse())
	}
	
	return responses, nil
}

// GetQuestionsByHashKey 根据哈希键获取所有相关题目
func (s *databaseService) GetQuestionsByHashKey(hashKey string) ([]*model.QuestionResponse, error) {
	// 根据哈希键查询数据库中所有相同的记录
	questions, err := s.questionRepo.GetAllByHashKey(hashKey)
	if err != nil {
		return nil, fmt.Errorf("failed to get questions by hash key: %w", err)
	}
	
	// 转换为响应格式
	var responses []*model.QuestionResponse
	for _, question := range questions {
		responses = append(responses, question.ToResponse())
	}
	
	return responses, nil
}

// UpdateQuestion 更新题目
func (s *databaseService) UpdateQuestion(id uint64, req *model.UpdateQuestionRequest) error {
	return s.questionRepo.Update(id, req)
}

// DeleteQuestion 删除题目
func (s *databaseService) DeleteQuestion(id uint64) error {
	return s.questionRepo.Delete(id)
}

// GetQuestionByID 根据ID获取题目
func (s *databaseService) GetQuestionByID(id uint64) (*model.Question, error) {
	return s.questionRepo.GetByID(id)
}

// ListQuestions 获取题目列表
func (s *databaseService) ListQuestions(req *model.QuestionListRequest) ([]*model.Question, int64, error) {
	return s.questionRepo.List(req)
}

// CreateQuestion 创建题目
func (s *databaseService) CreateQuestion(req *model.CreateQuestionRequest) (*model.Question, error) {
	// 构建Question模型
	question := &model.Question{
		QuestType:    req.QuestType,
		QuestContent: req.QuestContent,
		QuestOptions: req.QuestOptions,
		Answer:       req.Answer,
		Analysis:     req.Analysis,
		ImageURL:     req.ImageURL,
		IsVerified:   false,
	}
	
	// 生成哈希键
	questOptions := make(map[string]string)
	for k, v := range req.QuestOptions {
		if str, ok := v.(string); ok {
			questOptions[k] = str
		}
	}
	
	hashKey, hashRaw := utils.GenerateCacheKey(req.QuestType, req.QuestContent, questOptions)
	question.HashKey = hashKey
	question.HashRaw = &hashRaw
	
	// 保存到数据库
	if err := s.questionRepo.Create(question); err != nil {
		return nil, fmt.Errorf("failed to create question: %w", err)
	}
	
	return question, nil
}

// BatchSaveQuestions 批量保存题目
func (s *databaseService) BatchSaveQuestions(questions []*model.Question) error {
	for _, question := range questions {
		if err := s.questionRepo.Create(question); err != nil {
			return fmt.Errorf("failed to batch save question %d: %w", question.ID, err)
		}
	}
	return nil
}

// GetQuestionStatistics 获取题目统计信息
func (s *databaseService) GetQuestionStatistics() (map[string]interface{}, error) {
	// 这里可以实现统计逻辑，比如各类型题目数量、验证状态等
	// 暂时返回空的统计信息
	stats := make(map[string]interface{})
	
	// 可以添加具体的统计查询
	// 例如：
	// - 总题目数量
	// - 各类型题目数量
	// - 已验证/未验证题目数量
	// - 最近添加的题目数量等
	
	return stats, nil
}

// SearchQuestions 搜索题目
func (s *databaseService) SearchQuestions(keyword string, questType *model.QuestionType, isVerified *bool, page, pageSize int) ([]*model.Question, int64, error) {
	req := &model.QuestionListRequest{
		Page:       page,
		PageSize:   pageSize,
		QuestType:  questType,
		IsVerified: isVerified,
		Keyword:    keyword,
	}
	
	return s.questionRepo.List(req)
}

// ValidateQuestionData 验证题目数据
func (s *databaseService) ValidateQuestionData(question *model.Question) error {
	// 验证题目类型
	if !question.QuestType.IsValid() {
		return fmt.Errorf("invalid question type: %s", question.QuestType)
	}
	
	// 验证题目内容
	if question.QuestContent == "" {
		return fmt.Errorf("question content cannot be empty")
	}
	
	// 验证选项
	questOptions := make(map[string]string)
	for k, v := range question.QuestOptions {
		if str, ok := v.(string); ok {
			questOptions[k] = str
		}
	}
	
	if err := utils.ValidateQuestionOptions(question.QuestType, questOptions); err != nil {
		return fmt.Errorf("invalid question options: %w", err)
	}
	
	// 验证答案
	if len(question.Answer) == 0 {
		return fmt.Errorf("question answer cannot be empty")
	}
	
	// 验证解析
	if question.Analysis == "" {
		return fmt.Errorf("question analysis cannot be empty")
	}
	
	return nil
}
