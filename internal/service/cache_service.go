package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"qwen-solve/internal/model"
	processContext "qwen-solve/internal/context"
	"qwen-solve/pkg/redis"
	"qwen-solve/pkg/utils"
)

// CacheService 缓存服务接口
type CacheService interface {
	GenerateCacheKey(ctx *processContext.ProcessContext) error
	GetFromRedis(ctx *processContext.ProcessContext) ([]*model.QuestionResponse, error)
	WriteToRedis(ctx *processContext.ProcessContext, questions []*model.QuestionResponse) error
	DeleteFromRedis(hashKey string) error
}

// cacheService 缓存服务实现
type cacheService struct{}

// NewCacheService 创建缓存服务实例
func NewCacheService() CacheService {
	return &cacheService{}
}

// GenerateCacheKey 生成缓存键
func (s *cacheService) GenerateCacheKey(ctx *processContext.ProcessContext) error {
	// 使用工具函数生成缓存键
	hashKey, hashRaw := utils.GenerateCacheKey(ctx.QuestType, ctx.QuestContent, ctx.QuestOptions)
	
	// 保存到上下文
	ctx.HashKey = hashKey
	ctx.HashRaw = hashRaw
	
	return nil
}

// GetFromRedis 从Redis获取缓存数据
func (s *cacheService) GetFromRedis(ctx *processContext.ProcessContext) ([]*model.QuestionResponse, error) {
	redisCtx := context.Background()
	
	// 检查Redis中是否存在该键
	exists, err := redis.Exists(redisCtx, ctx.HashKey)
	if err != nil {
		return nil, fmt.Errorf("failed to check redis key existence: %w", err)
	}
	
	if !exists {
		// 键不存在，返回nil表示缓存未命中
		return nil, nil
	}
	
	// 获取缓存数据
	var questions []*model.QuestionResponse
	err = redis.GetJSON(redisCtx, ctx.HashKey, &questions)
	if err != nil {
		return nil, fmt.Errorf("failed to get data from redis: %w", err)
	}
	
	// 设置Redis命中标志
	ctx.RedisHit = true
	
	return questions, nil
}

// WriteToRedis 写入数据到Redis
func (s *cacheService) WriteToRedis(ctx *processContext.ProcessContext, questions []*model.QuestionResponse) error {
	redisCtx := context.Background()
	
	// 将数据写入Redis，设置为永久缓存（不过期）
	err := redis.SetJSON(redisCtx, ctx.HashKey, questions, 0) // 0表示永不过期
	if err != nil {
		return fmt.Errorf("failed to write data to redis: %w", err)
	}
	
	return nil
}

// DeleteFromRedis 从Redis删除缓存
func (s *cacheService) DeleteFromRedis(hashKey string) error {
	redisCtx := context.Background()
	
	err := redis.Delete(redisCtx, hashKey)
	if err != nil {
		return fmt.Errorf("failed to delete data from redis: %w", err)
	}
	
	return nil
}

// GetCacheInfo 获取缓存信息（用于调试和监控）
func (s *cacheService) GetCacheInfo(hashKey string) (map[string]interface{}, error) {
	redisCtx := context.Background()
	
	info := make(map[string]interface{})
	
	// 检查键是否存在
	exists, err := redis.Exists(redisCtx, hashKey)
	if err != nil {
		return nil, err
	}
	info["exists"] = exists
	
	if exists {
		// 获取TTL
		ttl, err := redis.TTL(redisCtx, hashKey)
		if err != nil {
			return nil, err
		}
		info["ttl"] = ttl.String()
		
		// 获取数据大小（通过获取数据计算）
		data, err := redis.Get(redisCtx, hashKey)
		if err != nil {
			return nil, err
		}
		info["size"] = len(data)
	}
	
	return info, nil
}

// ClearAllCache 清空所有缓存（谨慎使用）
func (s *cacheService) ClearAllCache() error {
	redisCtx := context.Background()
	
	// 获取所有以特定模式开头的键
	keys, err := redis.Keys(redisCtx, "*")
	if err != nil {
		return fmt.Errorf("failed to get cache keys: %w", err)
	}
	
	if len(keys) > 0 {
		err = redis.Delete(redisCtx, keys...)
		if err != nil {
			return fmt.Errorf("failed to delete cache keys: %w", err)
		}
	}
	
	return nil
}

// GetCacheStats 获取缓存统计信息
func (s *cacheService) GetCacheStats() (map[string]interface{}, error) {
	redisCtx := context.Background()
	
	stats := make(map[string]interface{})
	
	// 获取Redis信息
	info, err := redis.Info(redisCtx, "memory", "stats")
	if err != nil {
		return nil, fmt.Errorf("failed to get redis info: %w", err)
	}
	
	// 解析Redis信息
	lines := strings.Split(info, "\r\n")
	for _, line := range lines {
		if strings.Contains(line, ":") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])
				
				// 只保留我们关心的统计信息
				switch key {
				case "used_memory_human", "used_memory_peak_human", "total_commands_processed", "keyspace_hits", "keyspace_misses":
					stats[key] = value
				}
			}
		}
	}
	
	// 获取键的数量
	keys, err := redis.Keys(redisCtx, "*")
	if err == nil {
		stats["total_keys"] = len(keys)
	}
	
	return stats, nil
}

// BatchWriteToRedis 批量写入Redis
func (s *cacheService) BatchWriteToRedis(data map[string][]*model.QuestionResponse) error {
	redisCtx := context.Background()

	// 使用Pipeline批量操作
	pipe := redis.GetClient().Pipeline()

	for hashKey, questions := range data {
		jsonData, err := json.Marshal(questions)
		if err != nil {
			return fmt.Errorf("failed to marshal questions for key %s: %w", hashKey, err)
		}

		pipe.Set(redisCtx, hashKey, jsonData, 0) // 0表示永不过期
	}

	_, err := pipe.Exec(redisCtx)
	if err != nil {
		return fmt.Errorf("failed to batch write to redis: %w", err)
	}

	return nil
}

// ValidateCacheKey 验证缓存键格式
func (s *cacheService) ValidateCacheKey(hashKey string) error {
	if !utils.ValidateHashKey(hashKey) {
		return fmt.Errorf("invalid cache key format: %s", hashKey)
	}
	return nil
}
