package service

import (
	"fmt"

	"qwen-solve/internal/context"
	"qwen-solve/internal/model"
	"qwen-solve/internal/repository"
)

// LogService 日志服务接口
type LogService interface {
	CreateRequestLog(ctx *context.ProcessContext) error
	GetRequestLogByID(id uint64) (*model.RequestLog, error)
	DeleteRequestLog(id uint64) error
	ListRequestLogs(req *model.RequestLogListRequest) ([]*model.RequestLog, int64, error)
	GetRequestLogStatistics(req *model.RequestLogListRequest) (*model.RequestLogStatistics, error)
	GetErrorLogs(page, pageSize int) ([]*model.RequestLog, int64, error)
	GetRecentLogs(limit int) ([]*model.RequestLog, error)
}

// logService 日志服务实现
type logService struct {
	logRepo repository.RequestLogRepository
}

// NewLogService 创建日志服务实例
func NewLogService() LogService {
	return &logService{
		logRepo: repository.NewRequestLogRepository(),
	}
}

// CreateRequestLog 创建请求日志
func (s *logService) CreateRequestLog(ctx *context.ProcessContext) error {
	// 设置处理时间
	ctx.SetProcessingTime()
	
	// 转换为请求日志
	logReq := ctx.ToRequestLog()
	
	// 保存到数据库
	if err := s.logRepo.Create(logReq); err != nil {
		return fmt.Errorf("failed to create request log: %w", err)
	}
	
	return nil
}

// GetRequestLogByID 根据ID获取请求日志
func (s *logService) GetRequestLogByID(id uint64) (*model.RequestLog, error) {
	log, err := s.logRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get request log by id: %w", err)
	}
	
	if log == nil {
		return nil, fmt.Errorf("request log not found")
	}
	
	return log, nil
}

// DeleteRequestLog 删除请求日志（软删除）
func (s *logService) DeleteRequestLog(id uint64) error {
	if err := s.logRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete request log: %w", err)
	}
	
	return nil
}

// ListRequestLogs 获取请求日志列表
func (s *logService) ListRequestLogs(req *model.RequestLogListRequest) ([]*model.RequestLog, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	
	logs, total, err := s.logRepo.List(req)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list request logs: %w", err)
	}
	
	return logs, total, nil
}

// GetRequestLogStatistics 获取请求日志统计信息
func (s *logService) GetRequestLogStatistics(req *model.RequestLogListRequest) (*model.RequestLogStatistics, error) {
	stats, err := s.logRepo.GetStatistics(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get request log statistics: %w", err)
	}
	
	return stats, nil
}

// BatchDeleteRequestLogs 批量删除请求日志
func (s *logService) BatchDeleteRequestLogs(ids []uint64) error {
	for _, id := range ids {
		if err := s.logRepo.Delete(id); err != nil {
			return fmt.Errorf("failed to delete request log %d: %w", id, err)
		}
	}
	
	return nil
}

// GetRequestLogsByDateRange 根据日期范围获取请求日志
func (s *logService) GetRequestLogsByDateRange(startDate, endDate string, page, pageSize int) ([]*model.RequestLog, int64, error) {
	req := &model.RequestLogListRequest{
		Page:      page,
		PageSize:  pageSize,
		StartDate: startDate,
		EndDate:   endDate,
	}
	
	return s.ListRequestLogs(req)
}

// GetErrorLogs 获取错误日志
func (s *logService) GetErrorLogs(page, pageSize int) ([]*model.RequestLog, int64, error) {
	req := &model.RequestLogListRequest{
		Page:     page,
		PageSize: pageSize,
		Status:   "error",
	}
	
	return s.ListRequestLogs(req)
}

// GetSuccessLogs 获取成功日志
func (s *logService) GetSuccessLogs(page, pageSize int) ([]*model.RequestLog, int64, error) {
	req := &model.RequestLogListRequest{
		Page:     page,
		PageSize: pageSize,
		Status:   "success",
	}
	
	return s.ListRequestLogs(req)
}

// GetCacheHitLogs 获取缓存命中日志
func (s *logService) GetCacheHitLogs(cacheType string, page, pageSize int) ([]*model.RequestLog, int64, error) {
	req := &model.RequestLogListRequest{
		Page:     page,
		PageSize: pageSize,
	}
	
	switch cacheType {
	case "redis":
		isRedisHit := true
		req.IsRedisHit = &isRedisHit
	case "mysql":
		isMysqlHit := true
		req.IsMysqlHit = &isMysqlHit
	}
	
	return s.ListRequestLogs(req)
}

// SearchRequestLogs 搜索请求日志
func (s *logService) SearchRequestLogs(keyword string, page, pageSize int) ([]*model.RequestLog, int64, error) {
	req := &model.RequestLogListRequest{
		Page:     page,
		PageSize: pageSize,
		Keyword:  keyword,
	}
	
	return s.ListRequestLogs(req)
}

// GetDailyStatistics 获取每日统计信息
func (s *logService) GetDailyStatistics(date string) (*model.RequestLogStatistics, error) {
	req := &model.RequestLogListRequest{
		StartDate: date,
		EndDate:   date,
	}
	
	return s.GetRequestLogStatistics(req)
}

// GetRecentLogs 获取最近的日志
func (s *logService) GetRecentLogs(limit int) ([]*model.RequestLog, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	
	req := &model.RequestLogListRequest{
		Page:     1,
		PageSize: limit,
	}
	
	logs, _, err := s.ListRequestLogs(req)
	return logs, err
}

// CleanOldLogs 清理旧日志（可以根据配置的保留天数）
func (s *logService) CleanOldLogs(retentionDays int) error {
	// 这里可以实现清理逻辑
	// 例如删除超过指定天数的日志
	// 暂时返回nil，具体实现可以根据需求添加
	return nil
}

// ExportLogs 导出日志（返回日志数据，具体导出格式由调用方决定）
func (s *logService) ExportLogs(req *model.RequestLogListRequest) ([]*model.RequestLog, error) {
	// 设置较大的页面大小以获取所有数据
	req.PageSize = 10000 // 可以根据实际需求调整
	
	logs, _, err := s.ListRequestLogs(req)
	if err != nil {
		return nil, fmt.Errorf("failed to export logs: %w", err)
	}
	
	return logs, nil
}
