package service

import (
	"fmt"

	"qwen-solve/internal/config"
	"qwen-solve/internal/context"
	"qwen-solve/internal/model"
	"qwen-solve/pkg/utils"
)

// SolveService 解题服务接口
type SolveService interface {
	SolveImage(imageURL, requestIP, userAgent string) (*model.SolveImageResponse, error)
	ValidateImageOnly(imageURL string) error
	ReprocessImage(imageURL, requestIP, userAgent string) (*model.SolveImageResponse, error)
	GetSolveHistory(page, pageSize int) ([]*model.RequestLog, int64, error)
	GetSolveStatistics(startDate, endDate string) (*model.RequestLogStatistics, error)
	ClearCache(hashKey string) error
	RefreshCache(hashKey string) error
}

// solveService 解题服务实现
type solveService struct {
	imageService    ImageService
	qwenService     QwenService
	cacheService    CacheService
	databaseService DatabaseService
	logService      LogService
}

// NewSolveService 创建解题服务实例
func NewSolveService(cfg *config.Config) SolveService {
	return &solveService{
		imageService:    NewImageService(),
		qwenService:     NewQwenService(&cfg.Qwen),
		cacheService:    NewCacheService(),
		databaseService: NewDatabaseService(),
		logService:      NewLogService(),
	}
}

// SolveImage 图片解题主流程
func (s *solveService) SolveImage(imageURL, requestIP, userAgent string) (*model.SolveImageResponse, error) {
	// 1. 创建处理上下文
	requestID := utils.GenerateRequestID()
	ctx := context.NewProcessContext(requestID, imageURL, requestIP, userAgent)

	// 确保最后记录日志
	defer func() {
		if err := s.logService.CreateRequestLog(ctx); err != nil {
			// 日志记录失败不应该影响主流程，只记录错误
			fmt.Printf("Failed to create request log: %v\n", err)
		}
	}()

	// 2. 验证图片
	if err := s.imageService.ValidateImage(ctx); err != nil {
		return nil, err
	}

	// 3. 调用qwen-vl-plus识别图片
	if err := s.qwenService.CallQwenVlPlus(ctx); err != nil {
		return nil, err
	}

	// 4. 解析qwen返回的数据
	if err := s.qwenService.FormatQwenData(ctx); err != nil {
		return nil, err
	}

	// 5. 生成缓存键
	if err := s.cacheService.GenerateCacheKey(ctx); err != nil {
		return nil, err
	}

	// 6. 查询Redis缓存
	questions, err := s.cacheService.GetFromRedis(ctx)
	if err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeCacheError])
		return nil, err
	}

	// 如果Redis命中，直接返回结果
	if questions != nil {
		ctx.ResponseData = questions
		return &model.SolveImageResponse{
			Questions: questions,
			RequestID: requestID,
			Cached:    true,
			Source:    "redis",
		}, nil
	}

	// 7. Redis未命中，查询MySQL
	questions, err = s.databaseService.GetFromDatabase(ctx)
	if err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeDatabaseError])
		return nil, err
	}

	// 如果MySQL命中，回写Redis并返回结果
	if questions != nil {
		// 回写Redis
		if err := s.cacheService.WriteToRedis(ctx, questions); err != nil {
			// Redis写入失败不影响主流程，只记录错误
			fmt.Printf("Failed to write to redis: %v\n", err)
		}

		ctx.ResponseData = questions
		return &model.SolveImageResponse{
			Questions: questions,
			RequestID: requestID,
			Cached:    true,
			Source:    "mysql",
		}, nil
	}

	// 8. MySQL也未命中，调用qwen-plus生成答案
	if err := s.qwenService.CallQwenPlus(ctx); err != nil {
		return nil, err
	}

	// 9. 保存到数据库
	if err := s.databaseService.SaveToDatabase(ctx); err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeDatabaseError])
		return nil, err
	}

	// 10. 获取保存后的完整数据用于Redis回写
	questions, err = s.databaseService.GetQuestionsByHashKey(ctx.HashKey)
	if err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeDatabaseError])
		return nil, err
	}

	// 11. 回写Redis
	if err := s.cacheService.WriteToRedis(ctx, questions); err != nil {
		// Redis写入失败不影响主流程，只记录错误
		fmt.Printf("Failed to write to redis: %v\n", err)
	}

	// 12. 返回结果
	ctx.ResponseData = questions
	return &model.SolveImageResponse{
		Questions: questions,
		RequestID: requestID,
		Cached:    false,
		Source:    "qwen",
	}, nil
}

// GetSolveHistory 获取解题历史
func (s *solveService) GetSolveHistory(page, pageSize int) ([]*model.RequestLog, int64, error) {
	req := &model.RequestLogListRequest{
		Page:     page,
		PageSize: pageSize,
		Status:   "success",
	}

	return s.logService.ListRequestLogs(req)
}

// GetSolveStatistics 获取解题统计信息
func (s *solveService) GetSolveStatistics(startDate, endDate string) (*model.RequestLogStatistics, error) {
	req := &model.RequestLogListRequest{
		StartDate: startDate,
		EndDate:   endDate,
	}

	return s.logService.GetRequestLogStatistics(req)
}

// ReprocessImage 重新处理图片（强制不使用缓存）
func (s *solveService) ReprocessImage(imageURL, requestIP, userAgent string) (*model.SolveImageResponse, error) {
	// 创建处理上下文
	requestID := utils.GenerateRequestID()
	ctx := context.NewProcessContext(requestID, imageURL, requestIP, userAgent)

	// 确保最后记录日志
	defer func() {
		if err := s.logService.CreateRequestLog(ctx); err != nil {
			fmt.Printf("Failed to create request log: %v\n", err)
		}
	}()

	// 验证图片
	if err := s.imageService.ValidateImage(ctx); err != nil {
		return nil, err
	}

	// 调用qwen-vl-plus识别图片
	if err := s.qwenService.CallQwenVlPlus(ctx); err != nil {
		return nil, err
	}

	// 解析qwen返回的数据
	if err := s.qwenService.FormatQwenData(ctx); err != nil {
		return nil, err
	}

	// 生成缓存键
	if err := s.cacheService.GenerateCacheKey(ctx); err != nil {
		return nil, err
	}

	// 直接调用qwen-plus生成答案（跳过缓存查询）
	if err := s.qwenService.CallQwenPlus(ctx); err != nil {
		return nil, err
	}

	// 保存到数据库
	if err := s.databaseService.SaveToDatabase(ctx); err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeDatabaseError])
		return nil, err
	}

	// 获取保存后的完整数据
	questions, err := s.databaseService.GetQuestionsByHashKey(ctx.HashKey)
	if err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeDatabaseError])
		return nil, err
	}

	// 更新Redis缓存
	if err := s.cacheService.WriteToRedis(ctx, questions); err != nil {
		fmt.Printf("Failed to write to redis: %v\n", err)
	}

	// 返回结果
	ctx.ResponseData = questions
	return &model.SolveImageResponse{
		Questions: questions,
		RequestID: requestID,
		Cached:    false,
		Source:    "qwen",
	}, nil
}

// ClearCache 清除指定题目的缓存
func (s *solveService) ClearCache(hashKey string) error {
	return s.cacheService.DeleteFromRedis(hashKey)
}

// RefreshCache 刷新缓存（从数据库重新加载到Redis）
func (s *solveService) RefreshCache(hashKey string) error {
	// 从数据库获取数据
	questions, err := s.databaseService.GetQuestionsByHashKey(hashKey)
	if err != nil {
		return fmt.Errorf("failed to get questions from database: %w", err)
	}

	if len(questions) == 0 {
		// 如果数据库中没有数据，删除Redis中的缓存
		return s.cacheService.DeleteFromRedis(hashKey)
	}

	// 创建临时上下文用于Redis写入
	tempCtx := &context.ProcessContext{HashKey: hashKey}
	return s.cacheService.WriteToRedis(tempCtx, questions)
}

// ValidateImageOnly 仅验证图片（不进行解题）
func (s *solveService) ValidateImageOnly(imageURL string) error {
	return utils.ValidateImageURL(imageURL)
}
