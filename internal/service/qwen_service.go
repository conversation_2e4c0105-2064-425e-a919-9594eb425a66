package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"qwen-solve/internal/config"
	"qwen-solve/internal/context"
	"qwen-solve/internal/model"
	"qwen-solve/pkg/utils"
)

// QwenService Qwen API服务接口
type QwenService interface {
	CallQwenVlPlus(ctx *context.ProcessContext) error
	FormatQwenData(ctx *context.ProcessContext) error
	CallQwenPlus(ctx *context.ProcessContext) error
}

// qwenService Qwen API服务实现
type qwenService struct {
	config *config.QwenConfig
	client *http.Client
}

// NewQwenService 创建Qwen API服务实例
func NewQwenService(cfg *config.QwenConfig) QwenService {
	return &qwenService{
		config: cfg,
		client: &http.Client{
			Timeout: cfg.Timeout,
		},
	}
}

// CallQwenVlPlus 调用qwen-vl-plus模型识别图片
func (s *qwenService) CallQwenVlPlus(ctx *context.ProcessContext) error {
	// 构建请求体
	request := model.QwenDashScopeRequest{
		Input: model.QwenInput{
			Messages: []model.QwenDashScopeMessage{
				{
					Role: "system",
					Content: []model.QwenDashScopeContent{
						{
							Text: "严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}",
						},
					},
				},
				{
					Role: "user",
					Content: []model.QwenDashScopeContent{
						{
							Image: ctx.UserImageURL,
						},
						{
							Text: "精准且完整的识别问题,严格按照要求输出的完整json,其中的options字段的格式必须正确,选项字母为键，选项内容为值",
						},
					},
				},
			},
		},
		Model: "qwen-vl-plus",
		Parameters: model.QwenParameters{
			PresencePenalty:   1,
			RepetitionPenalty: 1,
			ResponseFormat: model.QwenResponseFormat{
				Type: "json_object",
			},
			Temperature: 0.2,
			TopK:        1,
			TopP:        0.01,
		},
	}

	// 发送请求
	response, err := s.sendRequest("/api/v1/services/aigc/multimodal-generation/generation", request)
	if err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeQwenAPIError])
		return err
	}

	// 保存原始响应数据
	ctx.QwenRawData = string(response)

	return nil
}

// FormatQwenData 解析qwen-vl-plus返回的数据
func (s *qwenService) FormatQwenData(ctx *context.ProcessContext) error {
	// 解析qwen响应
	var qwenResp model.QwenDashScopeResponse
	if err := json.Unmarshal([]byte(ctx.QwenRawData), &qwenResp); err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeParseError])
		return err
	}

	// 检查响应是否有效
	if len(qwenResp.Output.Choices) == 0 {
		err := fmt.Errorf("qwen response has no choices")
		ctx.SetError(err, model.ErrorMessages[model.CodeParseError])
		return err
	}

	// 获取内容
	content := qwenResp.Output.Choices[0].Message.Content

	// 解析JSON内容
	var vlResp model.QwenVLResponse
	if err := json.Unmarshal([]byte(content), &vlResp); err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeParseError])
		return err
	}

	// 1. 提取题目类型
	questType, err := s.extractQuestionType(vlResp.Qutext)
	if err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeQuestionTypeError])
		return err
	}
	ctx.QuestType = questType

	// 2. 提取题目内容
	questContent, err := s.extractQuestionContent(vlResp.Qutext)
	if err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeQuestionInvalid])
		return err
	}
	ctx.QuestContent = questContent

	// 3. 验证并提取选项
	if err := s.validateAndExtractOptions(questType, vlResp.Options, ctx); err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeQuestionInvalid])
		return err
	}

	return nil
}

// extractQuestionType 提取题目类型
func (s *qwenService) extractQuestionType(qutext string) (model.QuestionType, error) {
	// 使用正则表达式提取题目类型
	re := regexp.MustCompile(`[（(]?(单选题|多选题|判断题)[）)]?`)
	matches := re.FindStringSubmatch(qutext)
	
	if len(matches) < 2 {
		return "", fmt.Errorf("无法识别题目类型")
	}

	questType := model.QuestionType(matches[1])
	if !questType.IsValid() {
		return "", fmt.Errorf("无效的题目类型: %s", matches[1])
	}

	return questType, nil
}

// extractQuestionContent 提取题目内容
func (s *qwenService) extractQuestionContent(qutext string) (string, error) {
	// 使用正则表达式清洗题目内容
	re := regexp.MustCompile(`(?s)^[（(]?(单选题|多选题|判断题)[）)]?(\d+)[、.，,：:]?(.*)$`)
	matches := re.FindStringSubmatch(qutext)
	
	if len(matches) < 4 {
		return "", fmt.Errorf("无法提取题目内容")
	}

	content := strings.TrimSpace(matches[3])
	
	// 验证题干是否干净
	if err := utils.ValidateQuestionContent(content); err != nil {
		return "", err
	}

	return content, nil
}

// validateAndExtractOptions 验证并提取选项
func (s *qwenService) validateAndExtractOptions(questType model.QuestionType, options map[string]string, ctx *context.ProcessContext) error {
	// 验证选项数量
	if err := utils.ValidateQuestionOptions(questType, options); err != nil {
		return err
	}

	// 保存选项
	ctx.QuestOptions = options

	return nil
}

// CallQwenPlus 调用qwen-plus模型生成答案
func (s *qwenService) CallQwenPlus(ctx *context.ProcessContext) error {
	// 构建问题JSON字符串
	questionData := map[string]interface{}{
		"quest_type":   ctx.QuestType,
		"quest_content": ctx.QuestContent,
		"quest_option":  ctx.QuestOptions,
	}

	questionJSON, err := json.Marshal(questionData)
	if err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeParseError])
		return err
	}

	ctx.QwenParsed = string(questionJSON)

	// 构建请求体
	request := model.QwenTextGenerationRequest{
		Model: "qwen-plus",
		Input: model.QwenTextInput{
			Messages: []model.QwenDashScopeMessage{
				{
					Role: "system",
					Content: []model.QwenDashScopeContent{
						{
							Text: "严格标准返回json格式。示例{\"answer\":[{\"正确选项\":\"选项内容\"}],\"analysis\":\"答案解析\"}",
						},
					},
				},
				{
					Role: "user",
					Content: []model.QwenDashScopeContent{
						{
							Text: "必须给出权威答案,answer字段的格式必须正确,选项字母为键，选项内容为值" + string(questionJSON),
						},
					},
				},
			},
		},
		Parameters: model.QwenTextParameters{
			PresencePenalty:   1,
			RepetitionPenalty: 1,
			ResponseFormat: model.QwenResponseFormat{
				Type: "json_object",
			},
			Temperature: 0.2,
			TopK:        1,
			TopP:        0.01,
			ResultFormat: "message",
		},
	}

	// 发送请求
	response, err := s.sendRequest("/api/v1/services/aigc/text-generation/generation", request)
	if err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeQwenAPIError])
		return err
	}

	// 保存原始响应数据
	ctx.QwenPlusRaw = string(response)

	// 解析响应
	if err := s.parseQwenPlusResponse(ctx); err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeParseError])
		return err
	}

	return nil
}

// parseQwenPlusResponse 解析qwen-plus响应
func (s *qwenService) parseQwenPlusResponse(ctx *context.ProcessContext) error {
	// 解析外层响应
	var apiResp model.APIResponse
	if err := json.Unmarshal([]byte(ctx.QwenPlusRaw), &apiResp); err != nil {
		return err
	}

	// 解析data字段
	dataStr, ok := apiResp.Data.(string)
	if !ok {
		return fmt.Errorf("invalid data format")
	}

	var qwenResp model.QwenDashScopeResponse
	if err := json.Unmarshal([]byte(dataStr), &qwenResp); err != nil {
		return err
	}

	// 获取内容
	if len(qwenResp.Output.Choices) == 0 {
		return fmt.Errorf("no choices in response")
	}

	content := qwenResp.Output.Choices[0].Message.Content

	// 解析答案内容
	var plusResp model.QwenPlusResponse
	if err := json.Unmarshal([]byte(content), &plusResp); err != nil {
		return err
	}

	// 保存答案和解析
	ctx.Answer = plusResp.Answer
	ctx.Analysis = plusResp.Analysis

	return nil
}

// sendRequest 发送HTTP请求
func (s *qwenService) sendRequest(endpoint string, data interface{}) ([]byte, error) {
	// 序列化请求数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建请求
	url := s.config.BaseURL + endpoint
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.config.APIKey)

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	return body, nil
}
